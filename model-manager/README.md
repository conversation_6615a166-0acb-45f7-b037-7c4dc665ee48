# 模型管理服务

这是一个独立的Flask应用服务，专门用于设置和管理大语言模型。

## 功能

- AI平台管理（添加、编辑、删除平台）
- AI模型配置（模型信息、价格设置）
- 应用管理（创建应用、生成API密钥）
- 应用模型关联（为应用分配可用模型）
- Web管理界面

## 服务信息

- **端口**: 5001
- **连接数据库**: models-db (模型数据库)
- **服务类型**: model-manager

## 主要功能模块

### 平台管理
- 添加新的AI平台（如OpenAI、Claude等）
- 配置平台API密钥和Base URL
- 管理平台状态

### 模型管理
- 添加和配置AI模型
- 设置模型价格（输入/输出token价格）
- 管理模型可见性和状态

### 应用管理
- 创建新应用
- 生成和管理API密钥
- 为应用分配可用模型

## 启动服务

### 前置条件
确保模型数据库服务已启动：
```bash
# 在 app/models-database 目录下
docker-compose up -d
```

### 启动模型管理服务
```bash
# 在当前目录下
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 访问服务

- **Web界面**: http://localhost:5001
- **API接口**: http://localhost:5001/api/v1/
- **健康检查**: http://localhost:5001/api/v1/health

## 停止服务

```bash
docker-compose down
```

## 环境变量配置

主要配置项在 `.env` 文件中：

- `DATABASE_URL`: 模型数据库连接字符串
- `ADMIN_PASSWORD`: 管理员密码
- `SECRET_KEY`: Flask密钥（生产环境请修改）
- 各平台API密钥配置

## 依赖服务

- **models-db**: 模型数据库服务（必须先启动）

## 注意事项

1. 确保模型数据库服务已启动并可访问
2. 生产环境请修改默认密码和密钥
3. 服务启动后需要等待数据库连接建立
4. 可通过健康检查端点监控服务状态
