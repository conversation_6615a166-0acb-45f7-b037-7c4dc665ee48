# API服务只读化迁移说明

## 概述

根据系统架构要求，api-service（5002端口）已被修改为纯只读API服务。所有数据的增删改操作必须通过model-manager服务（5001端口）进行。

## 主要修改

### 1. 删除写入操作服务方法

**文件**: `services/database.py`

删除了以下服务类中的写入方法：

#### PlatformService
- ❌ `create()` - 创建平台
- ❌ `update()` - 更新平台  
- ❌ `delete()` - 删除平台
- ✅ `get_all()` - 获取所有平台（保留）
- ✅ `get_by_id()` - 根据ID获取平台（保留）
- ✅ `get_by_name()` - 根据名称获取平台（保留）

#### ModelService
- ❌ `create()` - 创建模型
- ❌ `update()` - 更新模型
- ❌ `delete()` - 删除模型
- ✅ `get_all()` - 获取所有模型（保留）
- ✅ `get_by_id()` - 根据ID获取模型（保留）
- ✅ `get_by_display_name()` - 根据显示名称获取模型（保留）

#### ApplicationService
- ❌ `create()` - 创建应用
- ❌ `update()` - 更新应用
- ❌ `delete()` - 删除应用
- ❌ `regenerate_api_key()` - 重新生成API密钥
- ✅ `get_all()` - 获取所有应用（保留）
- ✅ `get_by_id()` - 根据ID获取应用（保留）
- ✅ `get_by_name()` - 根据名称获取应用（保留）
- ✅ `get_by_api_key()` - 根据API密钥获取应用（保留）

#### AppModelService
- ❌ `create()` - 创建应用模型关联
- ❌ `update()` - 更新应用模型关联
- ❌ `delete()` - 删除应用模型关联
- ✅ `get_all()` - 获取所有应用模型关联（保留）
- ✅ `get_by_id()` - 根据ID获取应用模型关联（保留）
- ✅ `get_by_app_id()` - 根据应用ID获取关联模型（保留）
- ✅ `get_by_app_and_model()` - 根据应用和模型ID获取关联（保留）

#### DatabaseService
- ❌ 整个类已删除（包含数据库事务提交方法）

### 2. 删除表单验证器

**文件**: `utils/validators.py`

删除了所有表单验证类，因为API服务不需要表单处理：
- ❌ `PlatformForm`
- ❌ `ModelForm` 
- ❌ `ApplicationForm`
- ❌ `LoginForm`
- ❌ `AppModelForm`
- ❌ `validate_json_input()` 函数
- ❌ `sanitize_string()` 函数

### 3. 更新文档和注释

**文件**: `README.md`
- 明确说明服务为只读
- 强调所有写入操作必须通过model-manager服务进行
- 更新功能特性描述

**文件**: `app.py`
- 更新注释说明这是只读服务
- 更新API文档描述

**文件**: `blueprints/api.py`
- 移除不需要的导入

## 保留的功能

### API端点（全部为GET请求）
- `GET /api/v1/platforms` - 获取所有平台列表
- `GET /api/v1/platforms/{id}` - 获取特定平台详情
- `GET /api/v1/models` - 获取所有模型列表
- `GET /api/v1/models/{id}` - 获取特定模型详情
- `GET /api/v1/applications` - 获取应用列表（需要API密钥）
- `GET /api/v1/app/{api_key}/models` - 根据API密钥获取应用关联的模型
- `GET /api/v1/stats` - 获取系统统计信息
- `GET /api/v1/health` - 健康检查

### 查询功能
- 支持分页查询
- 支持条件过滤（如按平台、可见性、免费等）
- 支持统计信息查询

## 系统架构

```
┌─────────────────────┐    ┌─────────────────────┐
│   Model Manager     │    │    API Service      │
│     (5001端口)      │    │     (5002端口)      │
│                     │    │                     │
│  ✅ 增删改查操作    │    │  ✅ 只读查询操作    │
│  ✅ Web管理界面     │    │  ❌ 无写入操作      │
│  ✅ 表单验证        │    │  ❌ 无Web界面       │
│                     │    │                     │
└─────────────────────┘    └─────────────────────┘
           │                          │
           └──────────┬─────────────────┘
                      │
              ┌───────▼────────┐
              │  Models Database │
              │    (MySQL)       │
              └──────────────────┘
```

## 注意事项

1. **数据一致性**: 确保所有写入操作都通过model-manager服务进行
2. **权限控制**: API服务仍保留API密钥验证和速率限制
3. **数据库连接**: API服务仍需要数据库连接进行读取操作
4. **表结构**: `db.create_all()`保留，用于确保表结构存在

## 迁移完成

✅ API服务已成功转换为纯只读服务
✅ 所有写入操作代码已移除
✅ 文档已更新
✅ 系统架构清晰分离
