from flask import Blueprint, jsonify, request, current_app
from models import Platform, AIModel, Application, AppModel
from utils.security import api_enabled_required, validate_api_key, rate_limit
# validate_json_input函数已移除，API服务为只读服务
import logging

logger = logging.getLogger(__name__)

api_bp = Blueprint('api', __name__, url_prefix='/api/v1')

@api_bp.errorhandler(400)
def bad_request(error):
    """400错误处理"""
    return jsonify({
        'error': 'Bad Request',
        'message': '请求格式错误',
        'status_code': 400
    }), 400

@api_bp.errorhandler(401)
def unauthorized(error):
    """401错误处理"""
    return jsonify({
        'error': 'Unauthorized',
        'message': '未授权访问',
        'status_code': 401
    }), 401

@api_bp.errorhandler(403)
def forbidden(error):
    """403错误处理"""
    return jsonify({
        'error': 'Forbidden',
        'message': 'API功能已禁用',
        'status_code': 403
    }), 403

@api_bp.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        'error': 'Not Found',
        'message': '资源不存在',
        'status_code': 404
    }), 404

@api_bp.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    logger.error(f"Internal server error: {str(error)}")
    return jsonify({
        'error': 'Internal Server Error',
        'message': '服务器内部错误',
        'status_code': 500
    }), 500

# ======== 平台API ========
@api_bp.route('/platforms', methods=['GET'])
@api_enabled_required
@rate_limit(max_requests=100, window=3600)
def api_platforms():
    """获取所有平台"""
    try:
        platforms = Platform.query.all()
        return jsonify({
            'success': True,
            'data': [platform.to_dict() for platform in platforms],
            'count': len(platforms)
        })
    except Exception as e:
        logger.error(f"Error fetching platforms: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取平台列表失败'
        }), 500

@api_bp.route('/platforms/<int:platform_id>', methods=['GET'])
@api_enabled_required
@rate_limit(max_requests=100, window=3600)
def api_platform_detail(platform_id):
    """获取平台详情"""
    try:
        platform = Platform.query.get_or_404(platform_id)
        return jsonify({
            'success': True,
            'data': platform.to_dict()
        })
    except Exception as e:
        logger.error(f"Error fetching platform {platform_id}: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取平台详情失败'
        }), 500

# ======== 模型API ========
@api_bp.route('/models', methods=['GET'])
@api_enabled_required
@rate_limit(max_requests=100, window=3600)
def api_models():
    """获取所有模型"""
    try:
        # 支持查询参数过滤
        platform_id = request.args.get('platform_id', type=int)
        visible_only = request.args.get('visible_only', 'false').lower() == 'true'
        free_only = request.args.get('free_only', 'false').lower() == 'true'
        
        query = AIModel.query
        
        if platform_id:
            query = query.filter_by(platform_id=platform_id)
        if visible_only:
            query = query.filter_by(is_visible_model=True)
        if free_only:
            query = query.filter_by(free=True)
        
        models = query.all()
        
        return jsonify({
            'success': True,
            'data': [model.to_dict() for model in models],
            'count': len(models)
        })
    except Exception as e:
        logger.error(f"Error fetching models: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取模型列表失败'
        }), 500

@api_bp.route('/models/<int:model_id>', methods=['GET'])
@api_enabled_required
@rate_limit(max_requests=100, window=3600)
def api_model_detail(model_id):
    """获取模型详情"""
    try:
        model = AIModel.query.get_or_404(model_id)
        return jsonify({
            'success': True,
            'data': model.to_dict()
        })
    except Exception as e:
        logger.error(f"Error fetching model {model_id}: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取模型详情失败'
        }), 500

# ======== 应用API ========
@api_bp.route('/applications', methods=['GET'])
@api_enabled_required
@validate_api_key
@rate_limit(max_requests=100, window=3600)
def api_applications():
    """获取所有应用（需要API密钥）"""
    try:
        applications = Application.query.all()
        return jsonify({
            'success': True,
            'data': [app.to_dict() for app in applications],
            'count': len(applications)
        })
    except Exception as e:
        logger.error(f"Error fetching applications: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取应用列表失败'
        }), 500

@api_bp.route('/app/<string:api_key>/models', methods=['GET'])
@api_enabled_required
@rate_limit(max_requests=1000, window=3600)
def api_app_models_by_key(api_key):
    """根据API密钥获取应用关联的模型"""
    try:
        application = Application.query.filter_by(api_key=api_key).first()
        if not application:
            return jsonify({
                'success': False,
                'error': '无效的API密钥'
            }), 401
        
        app_models = AppModel.query.filter_by(application_id=application.id).all()
        models_data = []
        
        for am in app_models:
            model_data = am.model.to_dict()
            model_data['is_default'] = am.is_default
            model_data['association_id'] = am.id
            models_data.append(model_data)
        
        return jsonify({
            'success': True,
            'application': application.name,
            'data': models_data,
            'count': len(models_data)
        })
        
    except Exception as e:
        logger.error(f"Error fetching app models for key {api_key}: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取应用模型失败'
        }), 500

# ======== 统计API ========
@api_bp.route('/stats', methods=['GET'])
@api_enabled_required
@rate_limit(max_requests=50, window=3600)
def api_stats():
    """获取系统统计信息"""
    try:
        stats = {
            'platforms_count': Platform.query.count(),
            'models_count': AIModel.query.count(),
            'applications_count': Application.query.count(),
            'app_models_count': AppModel.query.count(),
            'visible_models_count': AIModel.query.filter_by(is_visible_model=True).count(),
            'free_models_count': AIModel.query.filter_by(free=True).count()
        }
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.error(f"Error fetching stats: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取统计信息失败'
        }), 500

# ======== 健康检查API ========
@api_bp.route('/health', methods=['GET'])
def api_health():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'version': current_app.config.get('API_VERSION', 'v1'),
        'api_enabled': current_app.config.get('API_ENABLED', False)
    })
